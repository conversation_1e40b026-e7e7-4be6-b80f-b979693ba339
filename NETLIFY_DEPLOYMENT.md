# 📷 Déploiement de l'Application Caméra sur Netlify

## 🚀 Guide de Déploiement

### 1. Prérequis
- Compte Netlify
- Repository GitHub/GitLab avec le code
- Node.js et npm installés localement

### 2. Configuration Netlify

#### A. Paramètres de Build
```
Build command: npm run build
Publish directory: dist
```

#### B. Variables d'Environnement (optionnel)
```
NODE_ENV=production
```

### 3. Configuration des Headers de Sécurité

Le fichier `netlify.toml` et `public/_headers` sont déjà configurés avec :

- **Permissions Policy** : Autorise l'accès à la caméra et au microphone
- **Feature Policy** : Fallback pour les anciens navigateurs
- **Content Security Policy** : Sécurise l'application
- **HTTPS Enforcement** : Force l'utilisation de HTTPS

### 4. Résolution des Problèmes Courants

#### ❌ Problème : "Permission denied" ou caméra ne fonctionne pas

**Solutions :**

1. **Vérifier HTTPS**
   - Assurez-vous que votre site Netlify utilise HTTPS
   - URL doit commencer par `https://`

2. **Permissions du Navigateur**
   - Cliquez sur l'icône de caméra dans la barre d'adresse
   - Autorisez l'accès à la caméra et au microphone
   - Rechargez la page

3. **Headers de Sécurité**
   - Vérifiez que les fichiers `netlify.toml` et `public/_headers` sont présents
   - Redéployez si nécessaire

4. **Navigateur Compatible**
   - Utilisez Chrome, Firefox, Safari ou Edge récents
   - Évitez les navigateurs très anciens

#### ❌ Problème : "Not secure context"

**Solutions :**
- Vérifiez que votre domaine Netlify utilise HTTPS
- Si vous utilisez un domaine personnalisé, configurez le SSL
- Attendez que le certificat SSL soit activé (peut prendre quelques minutes)

#### ❌ Problème : Headers non appliqués

**Solutions :**
1. Vérifiez que `public/_headers` est dans le dossier `public/`
2. Vérifiez que `netlify.toml` est à la racine du projet
3. Redéployez l'application
4. Videz le cache du navigateur

### 5. Test de Déploiement

1. **Déployez sur Netlify**
2. **Ouvrez l'application**
3. **Cliquez sur "🔧 Debug"** dans l'en-tête
4. **Vérifiez les informations :**
   - Contexte sécurisé : ✅ HTTPS
   - Environnement : 🌐 Netlify
   - MediaDevices supporté : ✅
   - getUserMedia supporté : ✅

5. **Testez les permissions** avec le bouton "🔐 Tester les permissions"

### 6. Commandes de Déploiement

#### Déploiement automatique (recommandé)
1. Connectez votre repository à Netlify
2. Configurez les paramètres de build
3. Chaque push déclenche un déploiement automatique

#### Déploiement manuel
```bash
# Build local
npm run build

# Déploiement avec Netlify CLI
npm install -g netlify-cli
netlify login
netlify deploy --prod --dir=dist
```

### 7. Domaine Personnalisé

Si vous utilisez un domaine personnalisé :

1. **Configurez le DNS** chez votre registrar
2. **Ajoutez le domaine** dans Netlify
3. **Activez le SSL** (automatique avec Let's Encrypt)
4. **Attendez la propagation** (jusqu'à 24h)

### 8. Monitoring et Debug

- Utilisez l'interface de debug intégrée (bouton 🔧)
- Consultez les logs Netlify en cas d'erreur de build
- Vérifiez la console du navigateur pour les erreurs JavaScript

### 9. Support

En cas de problème persistant :

1. Vérifiez les logs de déploiement Netlify
2. Testez en mode incognito
3. Testez sur différents appareils/navigateurs
4. Consultez la documentation Netlify sur les headers

---

## 📱 Compatibilité

- ✅ Chrome (Android/Desktop)
- ✅ Firefox (Android/Desktop)
- ✅ Safari (iOS/macOS)
- ✅ Edge (Desktop)
- ❌ Internet Explorer (non supporté)

## 🔒 Sécurité

L'application respecte les standards de sécurité modernes :
- HTTPS obligatoire
- Permissions explicites
- Headers de sécurité configurés
- Pas de stockage de données sensibles
