[build]
  publish = "dist"
  command = "npm run build"

[[headers]]
  for = "/*"
  [headers.values]
    # Permissions Policy pour autoriser la caméra et le microphone
    Permissions-Policy = "camera=*, microphone=*, geolocation=(), payment=(), usb=(), interest-cohort=()"
    
    # Feature Policy (fallback pour les anciens navigateurs)
    Feature-Policy = "camera '*'; microphone '*'; geolocation 'none'; payment 'none'; usb 'none'"
    
    # Content Security Policy
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; media-src 'self' blob:; connect-src 'self' https:; worker-src 'self' blob:;"
    
    # Autres headers de sécurité
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Cache headers
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

# Redirections pour SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Configuration pour forcer HTTPS
[context.production]
  command = "npm run build"
  
[context.deploy-preview]
  command = "npm run build"
  
[context.branch-deploy]
  command = "npm run build"
