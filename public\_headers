/*
  Permissions-Policy: camera=*, microphone=*, geolocation=(), payment=(), usb=(), interest-cohort=()
  Feature-Policy: camera '*'; microphone '*'; geolocation 'none'; payment 'none'; usb 'none'
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; media-src 'self' blob:; connect-src 'self' https:; worker-src 'self' blob:;
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
