#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification de la configuration Netlify...\n');

const checks = [];

// Vérifier netlify.toml
const netlifyTomlPath = path.join(process.cwd(), 'netlify.toml');
if (fs.existsSync(netlifyTomlPath)) {
  checks.push('✅ netlify.toml trouvé');
  
  const content = fs.readFileSync(netlifyTomlPath, 'utf8');
  if (content.includes('Permissions-Policy')) {
    checks.push('✅ Permissions-Policy configuré');
  } else {
    checks.push('❌ Permissions-Policy manquant');
  }
  
  if (content.includes('camera=*')) {
    checks.push('✅ Permission caméra configurée');
  } else {
    checks.push('❌ Permission caméra manquante');
  }
} else {
  checks.push('❌ netlify.toml manquant');
}

// Vérifier _headers
const headersPath = path.join(process.cwd(), 'public', '_headers');
if (fs.existsSync(headersPath)) {
  checks.push('✅ public/_headers trouvé');
  
  const content = fs.readFileSync(headersPath, 'utf8');
  if (content.includes('camera=*')) {
    checks.push('✅ Headers caméra configurés');
  } else {
    checks.push('❌ Headers caméra manquants');
  }
} else {
  checks.push('❌ public/_headers manquant');
}

// Vérifier package.json
const packagePath = path.join(process.cwd(), 'package.json');
if (fs.existsSync(packagePath)) {
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  if (pkg.scripts && pkg.scripts.build) {
    checks.push('✅ Script de build configuré');
  } else {
    checks.push('❌ Script de build manquant');
  }
  
  if (pkg.dependencies && pkg.dependencies['@tensorflow/tfjs']) {
    checks.push('✅ TensorFlow.js installé');
  } else {
    checks.push('❌ TensorFlow.js manquant');
  }
}

// Vérifier la structure des dossiers
const srcPath = path.join(process.cwd(), 'src');
const publicPath = path.join(process.cwd(), 'public');

if (fs.existsSync(srcPath)) {
  checks.push('✅ Dossier src trouvé');
} else {
  checks.push('❌ Dossier src manquant');
}

if (fs.existsSync(publicPath)) {
  checks.push('✅ Dossier public trouvé');
} else {
  checks.push('❌ Dossier public manquant');
}

// Afficher les résultats
console.log('📋 Résultats de la vérification:\n');
checks.forEach(check => console.log(check));

const errors = checks.filter(check => check.startsWith('❌'));
const success = checks.filter(check => check.startsWith('✅'));

console.log(`\n📊 Résumé: ${success.length} succès, ${errors.length} erreurs`);

if (errors.length === 0) {
  console.log('\n🎉 Configuration Netlify prête pour le déploiement!');
  process.exit(0);
} else {
  console.log('\n⚠️  Corrigez les erreurs avant de déployer sur Netlify.');
  console.log('\n📖 Consultez NETLIFY_DEPLOYMENT.md pour plus d\'informations.');
  process.exit(1);
}
