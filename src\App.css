/* Reset et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Styles de l'application - Mobile First */
.App {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 0.5rem;
  gap: 1rem;
}

.app-header {
  text-align: center;
  padding: 1rem 0.5rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 0.5rem;
}

.app-header h1 {
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.5px;
}

/* Styles de débogage */
.debug-toggle {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 0.3rem 0.6rem;
  font-size: 0.7rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.debug-toggle:hover {
  background: rgba(0, 0, 0, 0.2);
}

.debug-info {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  text-align: left;
}

.debug-info h3 {
  color: #2d3748;
  font-size: 1rem;
  margin-bottom: 0.8rem;
  text-align: center;
}

.debug-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.debug-item {
  font-size: 0.8rem;
  color: #4a5568;
  padding: 0.3rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 5px;
}

.debug-item strong {
  color: #2d3748;
}

.btn-debug {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
  color: #2d3748;
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  width: 100%;
}

.debug-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.netlify-help {
  background: linear-gradient(135deg, #e6fffa, #b2f5ea);
  border: 2px solid #38b2ac;
  border-radius: 10px;
  padding: 1rem;
  margin-top: 1rem;
}

.netlify-help h4 {
  color: #2c7a7b;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  text-align: center;
}

.netlify-help ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.netlify-help li {
  font-size: 0.75rem;
  color: #2d3748;
  padding: 0.2rem 0;
  border-bottom: 1px solid rgba(56, 178, 172, 0.2);
}

.netlify-help li:last-child {
  border-bottom: none;
}

.error-message {
  background: linear-gradient(135deg, #fed7d7, #fbb6ce);
  color: #c53030;
  padding: 0.8rem;
  border-radius: 12px;
  margin-top: 0.5rem;
  border: 1px solid #feb2b2;
  font-weight: 500;
  font-size: 0.9rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(197, 48, 48, 0.2);
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  padding-bottom: 2rem;
}

/* Section caméra - Mobile Optimized */
.camera-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin: 0;
}

.camera-info {
  text-align: center;
  margin-bottom: 1rem;
}

.camera-info h2 {
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
  letter-spacing: -0.3px;
}

.camera-count {
  color: #718096;
  font-size: 0.8rem;
  margin: 0;
  font-weight: 500;
}

.camera-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  width: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.camera-video {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  background: #000;
  display: block;
  object-fit: cover;
}

.detection-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.camera-placeholder {
  width: 100%;
  aspect-ratio: 16/9;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  border: 2px dashed #cbd5e0;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  font-size: 1rem;
  gap: 1rem;
  font-weight: 500;
}

.loading-spinner {
  width: 35px;
  height: 35px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recording-indicator {
  position: absolute;
  top: 0.8rem;
  right: 0.8rem;
  background: rgba(220, 38, 38, 0.95);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.8rem;
  animation: pulse 1.5s infinite;
  box-shadow: 0 3px 15px rgba(220, 38, 38, 0.4);
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.detection-indicator {
  position: absolute;
  top: 0.8rem;
  left: 0.8rem;
  background: rgba(34, 197, 94, 0.95);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 25px;
  font-weight: 700;
  font-size: 0.8rem;
  animation: pulse 1.5s infinite;
  box-shadow: 0 3px 15px rgba(34, 197, 94, 0.4);
  z-index: 10;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.camera-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  width: 100%;
}

.primary-controls {
  display: flex;
  gap: 0.8rem;
  justify-content: center;
  width: 100%;
}

.secondary-controls {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  width: 100%;
}

/* Boutons - Mobile Optimized */
.btn {
  padding: 1rem 1.2rem;
  border: none;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  min-height: 50px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:active::before {
  left: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):active {
  transform: scale(0.98);
}

.btn-photo {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  flex: 1;
}

.btn-record {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  flex: 1;
}

.btn-stop {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  flex: 1;
}

.btn-download {
  background: linear-gradient(135deg, #4ecdc4, #44a08d);
  color: white;
}

.btn-clear {
  background: linear-gradient(135deg, #a8a8a8, #8e8e8e);
  color: white;
}

.btn-switch {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #333;
  font-weight: 700;
}

.btn-detection {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #333;
  font-weight: 700;
}

.btn-detection-active {
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  color: #333;
  font-weight: 700;
  animation: pulse 2s infinite;
}

/* Sections média - Mobile Optimized */
.media-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.media-section h2 {
  color: #2d3748;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.3px;
}

.media-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.captured-photo,
.captured-video {
  width: 100%;
  max-height: 300px;
  border-radius: 15px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.media-controls {
  display: flex;
  gap: 0.8rem;
  width: 100%;
}

/* Section de détection d'objets - Mobile Optimized */
.detection-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
  border: 2px solid rgba(34, 197, 94, 0.3);
}

.detection-section h2 {
  color: #16a34a;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.3px;
}

.detections-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.8rem;
  max-height: 250px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.detections-grid::-webkit-scrollbar {
  width: 4px;
}

.detections-grid::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.detections-grid::-webkit-scrollbar-thumb {
  background: rgba(34, 197, 94, 0.5);
  border-radius: 10px;
}

.detection-item {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 15px;
  padding: 0.8rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.detection-item:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(34, 197, 94, 0.3);
}

.detection-class {
  font-weight: 600;
  color: #15803d;
  text-transform: capitalize;
  font-size: 0.9rem;
}

.detection-score {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  padding: 0.3rem 0.6rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

/* Responsive Design - Mobile First Approach */
@media (min-width: 768px) {
  .App {
    max-width: 768px;
    margin: 0 auto;
    padding: 1rem;
    gap: 1.5rem;
  }

  .app-header {
    padding: 1.5rem;
  }

  .app-header h1 {
    font-size: 2.2rem;
  }

  .camera-section,
  .media-section,
  .detection-section {
    padding: 1.5rem;
  }

  .camera-info h2 {
    font-size: 1.6rem;
  }

  .primary-controls {
    gap: 1rem;
  }

  .secondary-controls {
    flex-direction: row;
    gap: 1rem;
  }

  .btn {
    min-height: 55px;
    font-size: 1rem;
  }

  .detections-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    max-height: 300px;
  }
}

@media (min-width: 1024px) {
  .App {
    max-width: 900px;
    gap: 2rem;
  }

  .app-header h1 {
    font-size: 2.5rem;
  }

  .camera-section,
  .media-section,
  .detection-section {
    padding: 2rem;
  }
}

/* Animations et transitions */
.camera-section,
.media-section,
.detection-section {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Amélioration de l'accessibilité et UX mobile */
.btn:focus {
  outline: 2px solid #4299e1;
  outline-offset: 2px;
}

.btn:focus:not(:focus-visible) {
  outline: none;
}

.error-message {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-3px); }
  75% { transform: translateX(3px); }
}

/* Optimisations pour les écrans tactiles */
@media (hover: none) and (pointer: coarse) {
  .btn:hover {
    transform: none;
  }

  .detection-item:hover {
    transform: none;
  }
}

/* Amélioration de la lisibilité sur petits écrans */
@media (max-width: 360px) {
  .app-header h1 {
    font-size: 1.5rem;
  }

  .camera-info h2 {
    font-size: 1.1rem;
  }

  .media-section h2,
  .detection-section h2 {
    font-size: 1.2rem;
  }

  .btn {
    font-size: 0.85rem;
    padding: 0.9rem 1rem;
  }
}
