/* Reset et styles de base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Styles de l'application */
.App {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  gap: 2rem;
}

.app-header {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.app-header h1 {
  color: #4a5568;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #feb2b2;
  font-weight: 500;
}

.app-main {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  flex: 1;
}

/* Section caméra */
.camera-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.camera-info {
  text-align: center;
  margin-bottom: 1.5rem;
}

.camera-info h2 {
  color: #4a5568;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.camera-count {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
}

.camera-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.camera-video {
  width: 100%;
  max-width: 640px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  background: #000;
}

.camera-placeholder {
  width: 100%;
  max-width: 640px;
  height: 480px;
  background: #f7fafc;
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #718096;
  font-size: 1.2rem;
  gap: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.recording-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(220, 38, 38, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  animation: pulse 1.5s infinite;
  box-shadow: 0 2px 10px rgba(220, 38, 38, 0.3);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.camera-controls {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.primary-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.secondary-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Boutons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 160px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-photo {
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
}

.btn-record {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
}

.btn-stop {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
}

.btn-download {
  background: linear-gradient(135deg, #805ad5, #6b46c1);
  color: white;
}

.btn-clear {
  background: linear-gradient(135deg, #a0aec0, #718096);
  color: white;
}

.btn-switch {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  color: white;
  font-size: 0.9rem;
  min-width: 200px;
}

/* Sections média */
.media-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.media-section h2 {
  color: #4a5568;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 1.8rem;
}

.media-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.captured-photo,
.captured-video {
  max-width: 100%;
  width: auto;
  max-height: 400px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.media-controls {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .App {
    padding: 0.5rem;
    gap: 1rem;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .camera-section,
  .media-section {
    padding: 1rem;
  }

  .camera-video {
    max-width: 100%;
  }

  .camera-placeholder {
    height: 300px;
  }

  .primary-controls,
  .secondary-controls,
  .media-controls {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 280px;
  }

  .recording-indicator {
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .app-header h1 {
    font-size: 1.5rem;
  }

  .media-section h2 {
    font-size: 1.4rem;
  }

  .camera-placeholder {
    height: 250px;
    font-size: 1rem;
  }
}

/* Animations supplémentaires */
.camera-section,
.media-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Amélioration de l'accessibilité */
.btn:focus {
  outline: 3px solid #4299e1;
  outline-offset: 2px;
}

.error-message {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}
