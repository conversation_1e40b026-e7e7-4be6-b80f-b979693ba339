import React, { useState, useEffect, useRef } from 'react';
import './App.css';

function App() {
  const [stream, setStream] = useState(null);
  const [photo, setPhoto] = useState(null);
  const [video, setVideo] = useState(null);
  const [recording, setRecording] = useState(false);
  const [error, setError] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedBlobsRef = useRef([]);

  useEffect(() => {
    startCamera();
    return () => {
      // Cleanup: arrêter le stream quand le composant se démonte
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  const startCamera = async () => {
    try {
      setError(null);
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { width: 1280, height: 720 },
        audio: true
      });

      setStream(mediaStream);
      setCameraReady(true);

      // Attacher le stream à l'élément vidéo
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (err) {
      console.error('Erreur de configuration de la caméra :', err);
      setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
      setCameraReady(false);
    }
  };

  const handleTakePhoto = () => {
    if (!stream || !videoRef.current || !canvasRef.current) return;

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Définir les dimensions du canvas
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Dessiner l'image actuelle de la vidéo sur le canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convertir en blob
      canvas.toBlob((blob) => {
        setPhoto(blob);
      }, 'image/jpeg', 0.95);

    } catch (err) {
      console.error('Erreur de prise de photo :', err);
      setError('Erreur lors de la prise de photo.');
    }
  };

  const handleStartRecording = () => {
    if (!stream || recording) return;

    try {
      recordedBlobsRef.current = [];
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          recordedBlobsRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(recordedBlobsRef.current, { type: 'video/webm' });
        setVideo(videoBlob);
        setRecording(false);
      };

      mediaRecorder.onerror = (event) => {
        console.error('Erreur MediaRecorder:', event.error);
        setError('Erreur lors de l\'enregistrement.');
        setRecording(false);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(1000); // Enregistrer par chunks de 1 seconde
      setRecording(true);
      setError(null);

    } catch (err) {
      console.error('Erreur de démarrage d\'enregistrement :', err);
      setError('Impossible de démarrer l\'enregistrement.');
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current && recording) {
      mediaRecorderRef.current.stop();
    }
  };

  const handleDownloadPhoto = () => {
    if (!photo) return;

    try {
      const url = URL.createObjectURL(photo);
      const a = document.createElement('a');
      a.href = url;
      a.download = `photo_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.jpg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de photo :', err);
      setError('Erreur lors du téléchargement de la photo.');
    }
  };

  const handleDownloadVideo = () => {
    if (!video) return;

    try {
      const url = URL.createObjectURL(video);
      const a = document.createElement('a');
      a.href = url;
      a.download = `video_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de vidéo :', err);
      setError('Erreur lors du téléchargement de la vidéo.');
    }
  };

  const clearPhoto = () => {
    setPhoto(null);
  };

  const clearVideo = () => {
    setVideo(null);
  };

  return (
    <div className="App">
      <header className="app-header">
        <h1>📷 Application Caméra</h1>
        {error && <div className="error-message">{error}</div>}
      </header>

      <main className="app-main">
        {/* Section Caméra */}
        <section className="camera-section">
          <div className="camera-container">
            {cameraReady ? (
              <video
                ref={videoRef}
                className="camera-video"
                autoPlay
                playsInline
                muted
              />
            ) : (
              <div className="camera-placeholder">
                <p>Chargement de la caméra...</p>
              </div>
            )}
            {recording && <div className="recording-indicator">🔴 REC</div>}
          </div>

          <canvas ref={canvasRef} style={{ display: 'none' }} />

          <div className="camera-controls">
            <button
              onClick={handleTakePhoto}
              disabled={!cameraReady}
              className="btn btn-photo"
            >
              📸 Prendre une photo
            </button>

            {!recording ? (
              <button
                onClick={handleStartRecording}
                disabled={!cameraReady}
                className="btn btn-record"
              >
                🎥 Démarrer l'enregistrement
              </button>
            ) : (
              <button
                onClick={handleStopRecording}
                className="btn btn-stop"
              >
                ⏹️ Arrêter l'enregistrement
              </button>
            )}
          </div>
        </section>

        {/* Section Photos */}
        {photo && (
          <section className="media-section">
            <h2>📸 Photo capturée</h2>
            <div className="media-container">
              <img
                src={URL.createObjectURL(photo)}
                alt="Photo prise"
                className="captured-photo"
              />
              <div className="media-controls">
                <button onClick={handleDownloadPhoto} className="btn btn-download">
                  💾 Télécharger la photo
                </button>
                <button onClick={clearPhoto} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}

        {/* Section Vidéos */}
        {video && (
          <section className="media-section">
            <h2>🎥 Vidéo enregistrée</h2>
            <div className="media-container">
              <video
                controls
                className="captured-video"
                src={URL.createObjectURL(video)}
              />
              <div className="media-controls">
                <button onClick={handleDownloadVideo} className="btn btn-download">
                  💾 Télécharger la vidéo
                </button>
                <button onClick={clearVideo} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}
      </main>
    </div>
  );
}

export default App;