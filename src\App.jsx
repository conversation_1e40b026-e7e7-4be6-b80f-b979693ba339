import React, { useState, useEffect, useRef } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as cocoSsd from '@tensorflow-models/coco-ssd';
import './App.css';

function App() {
  const [stream, setStream] = useState(null);
  const [photo, setPhoto] = useState(null);
  const [video, setVideo] = useState(null);
  const [recording, setRecording] = useState(false);
  const [error, setError] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);
  const [currentCamera, setCurrentCamera] = useState('user'); // 'user' pour avant, 'environment' pour arrière
  const [availableCameras, setAvailableCameras] = useState([]);
  const [switchingCamera, setSwitchingCamera] = useState(false);

  // États pour la reconnaissance d'objets
  const [model, setModel] = useState(null);
  const [modelLoading, setModelLoading] = useState(false);
  const [detectionEnabled, setDetectionEnabled] = useState(false);
  const [detections, setDetections] = useState([]);
  const [detectionInterval, setDetectionInterval] = useState(null);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionCanvasRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedBlobsRef = useRef([]);

  useEffect(() => {
    initializeCamera();
    loadModel();
    return () => {
      // Cleanup: arrêter le stream quand le composant se démonte
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      // Arrêter la détection
      if (detectionInterval) {
        clearInterval(detectionInterval);
      }
    };
  }, []);

  // Effet pour changer de caméra
  useEffect(() => {
    if (availableCameras.length > 0) {
      startCamera(currentCamera);
    }
  }, [currentCamera]);

  const initializeCamera = async () => {
    try {
      // D'abord, obtenir la liste des caméras disponibles
      await getAvailableCameras();
      // Puis démarrer avec la caméra par défaut
      await startCamera(currentCamera);
    } catch (err) {
      console.error('Erreur d\'initialisation de la caméra :', err);
      setError('Impossible d\'initialiser la caméra.');
    }
  };

  const getAvailableCameras = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      setAvailableCameras(videoDevices);

      // Si aucune caméra n'est trouvée, essayer quand même
      if (videoDevices.length === 0) {
        console.warn('Aucune caméra détectée, tentative avec les contraintes par défaut');
      }
    } catch (err) {
      console.error('Erreur lors de l\'énumération des caméras :', err);
      // Continuer même si l'énumération échoue
    }
  };

  const startCamera = async (facingMode = 'user') => {
    try {
      setError(null);
      setSwitchingCamera(true);

      // Arrêter le stream existant
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      // Contraintes pour mobile et desktop
      const constraints = {
        video: {
          width: { ideal: 1280, max: 1920 },
          height: { ideal: 720, max: 1080 },
          facingMode: facingMode, // 'user' pour avant, 'environment' pour arrière
        },
        audio: true
      };

      // Pour les appareils mobiles, essayer d'abord avec facingMode exact
      let mediaStream;
      try {
        const exactConstraints = {
          ...constraints,
          video: {
            ...constraints.video,
            facingMode: { exact: facingMode }
          }
        };
        mediaStream = await navigator.mediaDevices.getUserMedia(exactConstraints);
      } catch (exactErr) {
        console.log('Contrainte exacte échouée, essai avec contrainte idéale');
        // Si exact échoue, essayer avec ideal
        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      }

      setStream(mediaStream);
      setCameraReady(true);
      setSwitchingCamera(false);

      // Attacher le stream à l'élément vidéo
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (err) {
      console.error('Erreur de configuration de la caméra :', err);
      setSwitchingCamera(false);

      if (err.name === 'NotAllowedError') {
        setError('Permission d\'accès à la caméra refusée. Veuillez autoriser l\'accès.');
      } else if (err.name === 'NotFoundError') {
        setError('Aucune caméra trouvée sur cet appareil.');
      } else if (err.name === 'OverconstrainedError') {
        setError('La caméra demandée n\'est pas disponible. Essayez de changer de caméra.');
      } else {
        setError('Impossible d\'accéder à la caméra. Vérifiez les permissions.');
      }
      setCameraReady(false);
    }
  };

  const handleTakePhoto = () => {
    if (!stream || !videoRef.current || !canvasRef.current) return;

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Définir les dimensions du canvas
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Dessiner l'image actuelle de la vidéo sur le canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convertir en blob
      canvas.toBlob((blob) => {
        setPhoto(blob);
      }, 'image/jpeg', 0.95);

    } catch (err) {
      console.error('Erreur de prise de photo :', err);
      setError('Erreur lors de la prise de photo.');
    }
  };

  const handleStartRecording = () => {
    if (!stream || recording) return;

    try {
      recordedBlobsRef.current = [];
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp9'
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          recordedBlobsRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(recordedBlobsRef.current, { type: 'video/webm' });
        setVideo(videoBlob);
        setRecording(false);
      };

      mediaRecorder.onerror = (event) => {
        console.error('Erreur MediaRecorder:', event.error);
        setError('Erreur lors de l\'enregistrement.');
        setRecording(false);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(1000); // Enregistrer par chunks de 1 seconde
      setRecording(true);
      setError(null);

    } catch (err) {
      console.error('Erreur de démarrage d\'enregistrement :', err);
      setError('Impossible de démarrer l\'enregistrement.');
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current && recording) {
      mediaRecorderRef.current.stop();
    }
  };

  const switchCamera = () => {
    if (recording) {
      setError('Impossible de changer de caméra pendant l\'enregistrement.');
      return;
    }

    const newCamera = currentCamera === 'user' ? 'environment' : 'user';
    setCurrentCamera(newCamera);
  };

  const getCameraLabel = () => {
    return currentCamera === 'user' ? 'Caméra avant' : 'Caméra arrière';
  };

  const getSwitchCameraLabel = () => {
    return currentCamera === 'user' ? '🔄 Passer à la caméra arrière' : '🔄 Passer à la caméra avant';
  };

  // Fonctions pour la reconnaissance d'objets
  const loadModel = async () => {
    try {
      setModelLoading(true);
      setError(null);

      // Initialiser TensorFlow.js
      await tf.ready();

      // Charger le modèle COCO-SSD
      const loadedModel = await cocoSsd.load();
      setModel(loadedModel);
      setModelLoading(false);

      console.log('Modèle de reconnaissance d\'objets chargé avec succès');
    } catch (err) {
      console.error('Erreur lors du chargement du modèle :', err);
      setError('Impossible de charger le modèle de reconnaissance d\'objets.');
      setModelLoading(false);
    }
  };

  const detectObjects = async () => {
    if (!model || !videoRef.current || !detectionCanvasRef.current) return;

    try {
      const video = videoRef.current;
      const canvas = detectionCanvasRef.current;
      const ctx = canvas.getContext('2d');

      // Ajuster la taille du canvas à la vidéo
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Effectuer la détection
      const predictions = await model.detect(video);

      // Mettre à jour les détections
      setDetections(predictions);

      // Dessiner les boîtes de détection
      drawDetections(ctx, predictions, canvas.width, canvas.height);

    } catch (err) {
      console.error('Erreur lors de la détection :', err);
    }
  };

  const drawDetections = (ctx, predictions, canvasWidth, canvasHeight) => {
    // Effacer le canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    predictions.forEach((prediction) => {
      const [x, y, width, height] = prediction.bbox;
      const score = prediction.score;
      const className = prediction.class;

      // Ne dessiner que les détections avec un score de confiance > 0.5
      if (score > 0.5) {
        // Dessiner la boîte
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, width, height);

        // Dessiner le label
        ctx.fillStyle = '#00ff00';
        ctx.font = '16px Arial';
        const text = `${className} (${Math.round(score * 100)}%)`;
        const textWidth = ctx.measureText(text).width;

        // Fond pour le texte
        ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
        ctx.fillRect(x, y - 25, textWidth + 10, 25);

        // Texte
        ctx.fillStyle = '#000000';
        ctx.fillText(text, x + 5, y - 7);
      }
    });
  };

  const toggleDetection = () => {
    if (!model) {
      setError('Le modèle de reconnaissance n\'est pas encore chargé.');
      return;
    }

    if (detectionEnabled) {
      // Arrêter la détection
      if (detectionInterval) {
        clearInterval(detectionInterval);
        setDetectionInterval(null);
      }
      setDetectionEnabled(false);
      setDetections([]);

      // Effacer le canvas de détection
      if (detectionCanvasRef.current) {
        const ctx = detectionCanvasRef.current.getContext('2d');
        ctx.clearRect(0, 0, detectionCanvasRef.current.width, detectionCanvasRef.current.height);
      }
    } else {
      // Démarrer la détection
      setDetectionEnabled(true);
      const interval = setInterval(detectObjects, 500); // Détection toutes les 500ms
      setDetectionInterval(interval);
    }
  };

  const handleDownloadPhoto = () => {
    if (!photo) return;

    try {
      const url = URL.createObjectURL(photo);
      const a = document.createElement('a');
      a.href = url;
      a.download = `photo_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.jpg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de photo :', err);
      setError('Erreur lors du téléchargement de la photo.');
    }
  };

  const handleDownloadVideo = () => {
    if (!video) return;

    try {
      const url = URL.createObjectURL(video);
      const a = document.createElement('a');
      a.href = url;
      a.download = `video_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de vidéo :', err);
      setError('Erreur lors du téléchargement de la vidéo.');
    }
  };

  const clearPhoto = () => {
    setPhoto(null);
  };

  const clearVideo = () => {
    setVideo(null);
  };

  return (
    <div className="App">
      <header className="app-header">
        <h1>📷 Application Caméra</h1>
        {error && <div className="error-message">{error}</div>}
      </header>

      <main className="app-main">
        {/* Section Caméra */}
        <section className="camera-section">
          <div className="camera-info">
            <h2>📹 {getCameraLabel()}</h2>
            {availableCameras.length > 1 && (
              <p className="camera-count">
                {availableCameras.length} caméra(s) disponible(s)
              </p>
            )}
          </div>

          <div className="camera-container">
            {cameraReady && !switchingCamera ? (
              <div className="video-container">
                <video
                  ref={videoRef}
                  className="camera-video"
                  autoPlay
                  playsInline
                  muted
                />
                <canvas
                  ref={detectionCanvasRef}
                  className="detection-canvas"
                />
              </div>
            ) : (
              <div className="camera-placeholder">
                <p>
                  {switchingCamera ? 'Changement de caméra...' : 'Chargement de la caméra...'}
                </p>
                {switchingCamera && <div className="loading-spinner"></div>}
              </div>
            )}
            {recording && <div className="recording-indicator">🔴 REC</div>}
            {detectionEnabled && <div className="detection-indicator">🤖 IA</div>}
          </div>

          <canvas ref={canvasRef} style={{ display: 'none' }} />

          <div className="camera-controls">
            <div className="primary-controls">
              <button
                onClick={handleTakePhoto}
                disabled={!cameraReady || switchingCamera}
                className="btn btn-photo"
              >
                📸 Prendre une photo
              </button>

              {!recording ? (
                <button
                  onClick={handleStartRecording}
                  disabled={!cameraReady || switchingCamera}
                  className="btn btn-record"
                >
                  🎥 Démarrer l'enregistrement
                </button>
              ) : (
                <button
                  onClick={handleStopRecording}
                  className="btn btn-stop"
                >
                  ⏹️ Arrêter l'enregistrement
                </button>
              )}
            </div>

            {/* Contrôles secondaires */}
            <div className="secondary-controls">
              <button
                onClick={switchCamera}
                disabled={!cameraReady || switchingCamera || recording}
                className="btn btn-switch"
                title={getSwitchCameraLabel()}
              >
                {getSwitchCameraLabel()}
              </button>

              <button
                onClick={toggleDetection}
                disabled={!cameraReady || switchingCamera || modelLoading}
                className={`btn ${detectionEnabled ? 'btn-detection-active' : 'btn-detection'}`}
                title={detectionEnabled ? 'Arrêter la reconnaissance d\'objets' : 'Démarrer la reconnaissance d\'objets'}
              >
                {modelLoading ? '⏳ Chargement IA...' :
                 detectionEnabled ? '🤖 Arrêter IA' : '🤖 Démarrer IA'}
              </button>
            </div>
          </div>
        </section>

        {/* Section Reconnaissance d'objets */}
        {detectionEnabled && detections.length > 0 && (
          <section className="detection-section">
            <h2>🤖 Objets détectés</h2>
            <div className="detections-grid">
              {detections
                .filter(detection => detection.score > 0.5)
                .map((detection, index) => (
                  <div key={index} className="detection-item">
                    <span className="detection-class">{detection.class}</span>
                    <span className="detection-score">
                      {Math.round(detection.score * 100)}%
                    </span>
                  </div>
                ))}
            </div>
          </section>
        )}

        {/* Section Photos */}
        {photo && (
          <section className="media-section">
            <h2>📸 Photo capturée</h2>
            <div className="media-container">
              <img
                src={URL.createObjectURL(photo)}
                alt="Photo prise"
                className="captured-photo"
              />
              <div className="media-controls">
                <button onClick={handleDownloadPhoto} className="btn btn-download">
                  💾 Télécharger la photo
                </button>
                <button onClick={clearPhoto} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}

        {/* Section Vidéos */}
        {video && (
          <section className="media-section">
            <h2>🎥 Vidéo enregistrée</h2>
            <div className="media-container">
              <video
                controls
                className="captured-video"
                src={URL.createObjectURL(video)}
              />
              <div className="media-controls">
                <button onClick={handleDownloadVideo} className="btn btn-download">
                  💾 Télécharger la vidéo
                </button>
                <button onClick={clearVideo} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}
      </main>
    </div>
  );
}

export default App;