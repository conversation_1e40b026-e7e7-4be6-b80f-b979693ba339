import React, { useState, useEffect, useRef } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as cocoSsd from '@tensorflow-models/coco-ssd';
import './App.css';

function App() {
  const [stream, setStream] = useState(null);
  const [photo, setPhoto] = useState(null);
  const [video, setVideo] = useState(null);
  const [recording, setRecording] = useState(false);
  const [error, setError] = useState(null);
  const [cameraReady, setCameraReady] = useState(false);
  const [currentCamera, setCurrentCamera] = useState('user'); // 'user' pour avant, 'environment' pour arrière
  const [availableCameras, setAvailableCameras] = useState([]);
  const [switchingCamera, setSwitchingCamera] = useState(false);

  // États pour la reconnaissance d'objets
  const [model, setModel] = useState(null);
  const [modelLoading, setModelLoading] = useState(false);
  const [detectionEnabled, setDetectionEnabled] = useState(false);
  const [detections, setDetections] = useState([]);
  const [detectionInterval, setDetectionInterval] = useState(null);
  const [debugInfo, setDebugInfo] = useState(null);
  const [showDebug, setShowDebug] = useState(false);

  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const detectionCanvasRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const recordedBlobsRef = useRef([]);

  useEffect(() => {
    console.log('🚀 Initialisation de l\'application caméra');
    logSystemInfo();
    initializeCamera();
    loadModel();
    return () => {
      console.log('🧹 Nettoyage des ressources');
      // Cleanup: arrêter le stream quand le composant se démonte
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
      // Arrêter la détection
      if (detectionInterval) {
        clearInterval(detectionInterval);
      }
    };
  }, []);

  // Effet pour changer de caméra
  useEffect(() => {
    if (availableCameras.length > 0) {
      startCamera(currentCamera);
    }
  }, [currentCamera]);

  const initializeCamera = async () => {
    try {
      // D'abord, obtenir la liste des caméras disponibles
      await getAvailableCameras();
      // Puis démarrer avec la caméra par défaut
      await startCamera(currentCamera);
    } catch (err) {
      console.error('Erreur d\'initialisation de la caméra :', err);
      setError('Impossible d\'initialiser la caméra.');
    }
  };

  const getAvailableCameras = async () => {
    try {
      console.log('🔍 Recherche des caméras disponibles...');

      // Demander d'abord les permissions pour avoir accès aux labels des caméras
      const tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
      tempStream.getTracks().forEach(track => track.stop());

      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');

      console.log(`📹 ${videoDevices.length} caméra(s) trouvée(s):`, videoDevices.map(d => ({
        id: d.deviceId,
        label: d.label || 'Caméra sans nom'
      })));

      setAvailableCameras(videoDevices);

      // Si aucune caméra n'est trouvée, essayer quand même
      if (videoDevices.length === 0) {
        console.warn('⚠️ Aucune caméra détectée, tentative avec les contraintes par défaut');
      }
    } catch (err) {
      console.error('❌ Erreur lors de l\'énumération des caméras :', err);
      // Continuer même si l'énumération échoue
      console.log('🔄 Continuation sans énumération des caméras');
    }
  };

  const startCamera = async (facingMode = 'user') => {
    try {
      console.log(`📱 Démarrage de la caméra avec facingMode: ${facingMode}`);
      setError(null);
      setSwitchingCamera(true);

      // Arrêter le stream existant
      if (stream) {
        console.log('🛑 Arrêt du stream existant');
        stream.getTracks().forEach(track => track.stop());
      }

      // Contraintes progressives pour une meilleure compatibilité
      const constraintsList = [
        // Contrainte 1: Haute qualité avec facingMode exact
        {
          video: {
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            facingMode: { exact: facingMode },
            frameRate: { ideal: 30, max: 60 }
          },
          audio: true
        },
        // Contrainte 2: Qualité moyenne avec facingMode ideal
        {
          video: {
            width: { ideal: 640, max: 1280 },
            height: { ideal: 480, max: 720 },
            facingMode: { ideal: facingMode },
            frameRate: { ideal: 30 }
          },
          audio: true
        },
        // Contrainte 3: Qualité de base avec facingMode simple
        {
          video: {
            facingMode: facingMode,
            frameRate: { ideal: 30 }
          },
          audio: true
        },
        // Contrainte 4: Très basique sans audio
        {
          video: {
            facingMode: facingMode
          },
          audio: false
        },
        // Contrainte 5: Dernière chance - juste vidéo
        {
          video: true,
          audio: false
        }
      ];

      let mediaStream = null;
      let lastError = null;

      for (let i = 0; i < constraintsList.length; i++) {
        try {
          console.log(`🔄 Tentative ${i + 1}/${constraintsList.length} avec contraintes:`, constraintsList[i]);
          mediaStream = await navigator.mediaDevices.getUserMedia(constraintsList[i]);
          console.log(`✅ Succès avec la tentative ${i + 1}`);
          break;
        } catch (err) {
          console.warn(`❌ Tentative ${i + 1} échouée:`, err.name, err.message);
          lastError = err;

          if (i === constraintsList.length - 1) {
            throw lastError;
          }
        }
      }

      if (!mediaStream) {
        throw new Error('Impossible d\'obtenir un stream vidéo');
      }

      // Analyser le stream obtenu
      const videoTrack = mediaStream.getVideoTracks()[0];
      if (videoTrack) {
        const settings = videoTrack.getSettings();
        console.log('📹 Paramètres de la caméra:', {
          width: settings.width,
          height: settings.height,
          frameRate: settings.frameRate,
          facingMode: settings.facingMode,
          deviceId: settings.deviceId
        });
      }

      setStream(mediaStream);
      setCameraReady(true);
      setSwitchingCamera(false);

      // Attacher le stream à l'élément vidéo
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        console.log('📺 Stream attaché à l\'élément vidéo');
      }

    } catch (err) {
      console.error('❌ Erreur finale de configuration de la caméra :', err);
      setSwitchingCamera(false);

      let errorMessage = 'Erreur inconnue lors de l\'accès à la caméra.';

      switch (err.name) {
        case 'NotAllowedError':
          errorMessage = 'Permission d\'accès à la caméra refusée. Veuillez autoriser l\'accès dans les paramètres de votre navigateur.';
          break;
        case 'NotFoundError':
          errorMessage = 'Aucune caméra trouvée sur cet appareil.';
          break;
        case 'NotReadableError':
          errorMessage = 'La caméra est déjà utilisée par une autre application.';
          break;
        case 'OverconstrainedError':
          errorMessage = 'La caméra demandée n\'est pas disponible. Essayez de changer de caméra.';
          break;
        case 'SecurityError':
          errorMessage = 'Accès à la caméra bloqué pour des raisons de sécurité. Vérifiez que vous utilisez HTTPS.';
          break;
        default:
          errorMessage = `Erreur: ${err.message}`;
      }

      console.error('📱 Message d\'erreur utilisateur:', errorMessage);
      setError(errorMessage);
      setCameraReady(false);
    }
  };

  const handleTakePhoto = () => {
    console.log('📸 Tentative de prise de photo...');

    if (!stream || !videoRef.current || !canvasRef.current) {
      const errorMsg = 'Ressources non disponibles pour la prise de photo';
      console.error('❌', errorMsg, { stream: !!stream, video: !!videoRef.current, canvas: !!canvasRef.current });
      setError(errorMsg);
      return;
    }

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Vérifier que la vidéo est prête
      if (video.videoWidth === 0 || video.videoHeight === 0) {
        throw new Error('La vidéo n\'est pas encore prête');
      }

      // Définir les dimensions du canvas
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      console.log(`📐 Dimensions de la photo: ${canvas.width}x${canvas.height}`);

      // Dessiner l'image actuelle de la vidéo sur le canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convertir en blob
      canvas.toBlob((blob) => {
        if (blob) {
          console.log(`✅ Photo capturée: ${(blob.size / 1024).toFixed(1)} KB`);
          setPhoto(blob);
          setError(null);
        } else {
          throw new Error('Impossible de créer le blob de la photo');
        }
      }, 'image/jpeg', 0.95);

    } catch (err) {
      console.error('❌ Erreur de prise de photo :', err);
      setError(`Erreur lors de la prise de photo: ${err.message}`);
    }
  };

  const handleStartRecording = () => {
    console.log('🎥 Tentative de démarrage d\'enregistrement...');

    if (!stream) {
      const errorMsg = 'Aucun stream vidéo disponible pour l\'enregistrement';
      console.error('❌', errorMsg);
      setError(errorMsg);
      return;
    }

    if (recording) {
      console.warn('⚠️ Enregistrement déjà en cours');
      return;
    }

    try {
      recordedBlobsRef.current = [];

      // Tester les formats supportés
      const supportedTypes = [
        'video/webm;codecs=vp9,opus',
        'video/webm;codecs=vp8,opus',
        'video/webm;codecs=h264,opus',
        'video/webm',
        'video/mp4'
      ];

      let selectedType = 'video/webm';
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          selectedType = type;
          console.log(`✅ Format sélectionné: ${type}`);
          break;
        }
      }

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: selectedType
      });

      console.log(`📹 MediaRecorder créé avec le type: ${selectedType}`);

      mediaRecorder.ondataavailable = (event) => {
        if (event.data && event.data.size > 0) {
          console.log(`📦 Chunk reçu: ${(event.data.size / 1024).toFixed(1)} KB`);
          recordedBlobsRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(recordedBlobsRef.current, { type: selectedType });
        console.log(`✅ Enregistrement terminé: ${(videoBlob.size / 1024 / 1024).toFixed(1)} MB`);
        setVideo(videoBlob);
        setRecording(false);
      };

      mediaRecorder.onerror = (event) => {
        console.error('❌ Erreur MediaRecorder:', event.error);
        setError(`Erreur lors de l'enregistrement: ${event.error?.message || 'Erreur inconnue'}`);
        setRecording(false);
      };

      mediaRecorder.onstart = () => {
        console.log('🎬 Enregistrement démarré');
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(1000); // Enregistrer par chunks de 1 seconde
      setRecording(true);
      setError(null);

    } catch (err) {
      console.error('❌ Erreur de démarrage d\'enregistrement :', err);
      setError(`Impossible de démarrer l'enregistrement: ${err.message}`);
    }
  };

  const handleStopRecording = () => {
    if (mediaRecorderRef.current && recording) {
      mediaRecorderRef.current.stop();
    }
  };

  const switchCamera = async () => {
    console.log('🔄 Tentative de changement de caméra...');

    if (recording) {
      const errorMsg = 'Impossible de changer de caméra pendant l\'enregistrement.';
      console.warn('⚠️', errorMsg);
      setError(errorMsg);
      return;
    }

    try {
      const newCamera = currentCamera === 'user' ? 'environment' : 'user';
      console.log(`📱 Changement de ${currentCamera} vers ${newCamera}`);
      setCurrentCamera(newCamera);
    } catch (err) {
      console.error('❌ Erreur lors du changement de caméra:', err);
      setError('Erreur lors du changement de caméra.');
    }
  };

  const getCameraLabel = () => {
    return currentCamera === 'user' ? 'Caméra avant' : 'Caméra arrière';
  };

  const getSwitchCameraLabel = () => {
    return currentCamera === 'user' ? '🔄 Passer à la caméra arrière' : '🔄 Passer à la caméra avant';
  };

  // Fonctions pour la reconnaissance d'objets
  const loadModel = async () => {
    try {
      setModelLoading(true);
      setError(null);

      // Initialiser TensorFlow.js
      await tf.ready();

      // Charger le modèle COCO-SSD
      const loadedModel = await cocoSsd.load();
      setModel(loadedModel);
      setModelLoading(false);

      console.log('Modèle de reconnaissance d\'objets chargé avec succès');
    } catch (err) {
      console.error('Erreur lors du chargement du modèle :', err);
      setError('Impossible de charger le modèle de reconnaissance d\'objets.');
      setModelLoading(false);
    }
  };

  const detectObjects = async () => {
    if (!model || !videoRef.current || !detectionCanvasRef.current) return;

    try {
      const video = videoRef.current;
      const canvas = detectionCanvasRef.current;
      const ctx = canvas.getContext('2d');

      // Ajuster la taille du canvas à la vidéo
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Effectuer la détection
      const predictions = await model.detect(video);

      // Mettre à jour les détections
      setDetections(predictions);

      // Dessiner les boîtes de détection
      drawDetections(ctx, predictions, canvas.width, canvas.height);

    } catch (err) {
      console.error('Erreur lors de la détection :', err);
    }
  };

  const drawDetections = (ctx, predictions, canvasWidth, canvasHeight) => {
    // Effacer le canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    predictions.forEach((prediction) => {
      const [x, y, width, height] = prediction.bbox;
      const score = prediction.score;
      const className = prediction.class;

      // Ne dessiner que les détections avec un score de confiance > 0.5
      if (score > 0.5) {
        // Dessiner la boîte
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, width, height);

        // Dessiner le label
        ctx.fillStyle = '#00ff00';
        ctx.font = '16px Arial';
        const text = `${className} (${Math.round(score * 100)}%)`;
        const textWidth = ctx.measureText(text).width;

        // Fond pour le texte
        ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
        ctx.fillRect(x, y - 25, textWidth + 10, 25);

        // Texte
        ctx.fillStyle = '#000000';
        ctx.fillText(text, x + 5, y - 7);
      }
    });
  };

  const toggleDetection = () => {
    if (!model) {
      setError('Le modèle de reconnaissance n\'est pas encore chargé.');
      return;
    }

    if (detectionEnabled) {
      // Arrêter la détection
      console.log('🛑 Arrêt de la détection d\'objets');
      if (detectionInterval) {
        clearInterval(detectionInterval);
        setDetectionInterval(null);
      }
      setDetectionEnabled(false);
      setDetections([]);

      // Effacer le canvas de détection
      if (detectionCanvasRef.current) {
        const ctx = detectionCanvasRef.current.getContext('2d');
        ctx.clearRect(0, 0, detectionCanvasRef.current.width, detectionCanvasRef.current.height);
      }
    } else {
      // Démarrer la détection
      console.log('🤖 Démarrage de la détection d\'objets');
      setDetectionEnabled(true);
      const interval = setInterval(detectObjects, 500); // Détection toutes les 500ms
      setDetectionInterval(interval);
    }
  };

  // Fonctions de débogage
  const logSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      screen: {
        width: screen.width,
        height: screen.height,
        availWidth: screen.availWidth,
        availHeight: screen.availHeight,
        colorDepth: screen.colorDepth
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      mediaDevicesSupported: !!navigator.mediaDevices,
      getUserMediaSupported: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      enumerateDevicesSupported: !!(navigator.mediaDevices && navigator.mediaDevices.enumerateDevices),
      isSecureContext: window.isSecureContext,
      location: window.location.protocol + '//' + window.location.host
    };

    console.log('📱 Informations système:', info);
    setDebugInfo(info);
    return info;
  };

  const toggleDebugInfo = () => {
    setShowDebug(!showDebug);
    if (!showDebug) {
      logSystemInfo();
    }
  };

  const testCameraPermissions = async () => {
    try {
      console.log('🔐 Test des permissions caméra...');
      const result = await navigator.permissions.query({ name: 'camera' });
      console.log('📋 État des permissions caméra:', result.state);

      if (result.state === 'denied') {
        setError('Permissions caméra refusées. Veuillez les autoriser dans les paramètres du navigateur.');
      } else if (result.state === 'prompt') {
        console.log('❓ Permissions caméra en attente de réponse utilisateur');
      } else {
        console.log('✅ Permissions caméra accordées');
      }

      return result.state;
    } catch (err) {
      console.warn('⚠️ Impossible de vérifier les permissions:', err);
      return 'unknown';
    }
  };

  const handleDownloadPhoto = () => {
    if (!photo) return;

    try {
      const url = URL.createObjectURL(photo);
      const a = document.createElement('a');
      a.href = url;
      a.download = `photo_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.jpg`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de photo :', err);
      setError('Erreur lors du téléchargement de la photo.');
    }
  };

  const handleDownloadVideo = () => {
    if (!video) return;

    try {
      const url = URL.createObjectURL(video);
      const a = document.createElement('a');
      a.href = url;
      a.download = `video_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erreur de téléchargement de vidéo :', err);
      setError('Erreur lors du téléchargement de la vidéo.');
    }
  };

  const clearPhoto = () => {
    setPhoto(null);
  };

  const clearVideo = () => {
    setVideo(null);
  };

  return (
    <div className="App">
      <header className="app-header">
        <h1>📷 Application Caméra</h1>
        {error && <div className="error-message">{error}</div>}

        {/* Bouton de débogage */}
        <button
          onClick={toggleDebugInfo}
          className="debug-toggle"
          title="Afficher/Masquer les informations de débogage"
        >
          🔧 Debug
        </button>

        {/* Informations de débogage */}
        {showDebug && debugInfo && (
          <div className="debug-info">
            <h3>🔍 Informations de débogage</h3>
            <div className="debug-grid">
              <div className="debug-item">
                <strong>Navigateur:</strong> {debugInfo.userAgent.split(' ').slice(-2).join(' ')}
              </div>
              <div className="debug-item">
                <strong>Écran:</strong> {debugInfo.screen.width}x{debugInfo.screen.height}
              </div>
              <div className="debug-item">
                <strong>Viewport:</strong> {debugInfo.viewport.width}x{debugInfo.viewport.height}
              </div>
              <div className="debug-item">
                <strong>MediaDevices:</strong> {debugInfo.mediaDevicesSupported ? '✅' : '❌'}
              </div>
              <div className="debug-item">
                <strong>getUserMedia:</strong> {debugInfo.getUserMediaSupported ? '✅' : '❌'}
              </div>
              <div className="debug-item">
                <strong>Contexte sécurisé:</strong> {debugInfo.isSecureContext ? '✅ HTTPS' : '❌ HTTP'}
              </div>
              <div className="debug-item">
                <strong>Caméras disponibles:</strong> {availableCameras.length}
              </div>
              <div className="debug-item">
                <strong>Caméra actuelle:</strong> {currentCamera === 'user' ? '📱 Avant' : '📷 Arrière'}
              </div>
            </div>

            <button
              onClick={testCameraPermissions}
              className="btn btn-debug"
            >
              🔐 Tester les permissions
            </button>
          </div>
        )}
      </header>

      <main className="app-main">
        {/* Section Caméra */}
        <section className="camera-section">
          <div className="camera-info">
            <h2>📹 {getCameraLabel()}</h2>
            {availableCameras.length > 1 && (
              <p className="camera-count">
                {availableCameras.length} caméra(s) disponible(s)
              </p>
            )}
          </div>

          <div className="camera-container">
            {cameraReady && !switchingCamera ? (
              <div className="video-container">
                <video
                  ref={videoRef}
                  className="camera-video"
                  autoPlay
                  playsInline
                  muted
                />
                <canvas
                  ref={detectionCanvasRef}
                  className="detection-canvas"
                />
              </div>
            ) : (
              <div className="camera-placeholder">
                <p>
                  {switchingCamera ? 'Changement de caméra...' : 'Chargement de la caméra...'}
                </p>
                {switchingCamera && <div className="loading-spinner"></div>}
              </div>
            )}
            {recording && <div className="recording-indicator">🔴 REC</div>}
            {detectionEnabled && <div className="detection-indicator">🤖 IA</div>}
          </div>

          <canvas ref={canvasRef} style={{ display: 'none' }} />

          <div className="camera-controls">
            <div className="primary-controls">
              <button
                onClick={handleTakePhoto}
                disabled={!cameraReady || switchingCamera}
                className="btn btn-photo"
              >
                📸 Prendre une photo
              </button>

              {!recording ? (
                <button
                  onClick={handleStartRecording}
                  disabled={!cameraReady || switchingCamera}
                  className="btn btn-record"
                >
                  🎥 Démarrer l'enregistrement
                </button>
              ) : (
                <button
                  onClick={handleStopRecording}
                  className="btn btn-stop"
                >
                  ⏹️ Arrêter l'enregistrement
                </button>
              )}
            </div>

            {/* Contrôles secondaires */}
            <div className="secondary-controls">
              <button
                onClick={switchCamera}
                disabled={!cameraReady || switchingCamera || recording}
                className="btn btn-switch"
                title={getSwitchCameraLabel()}
              >
                {getSwitchCameraLabel()}
              </button>

              <button
                onClick={toggleDetection}
                disabled={!cameraReady || switchingCamera || modelLoading}
                className={`btn ${detectionEnabled ? 'btn-detection-active' : 'btn-detection'}`}
                title={detectionEnabled ? 'Arrêter la reconnaissance d\'objets' : 'Démarrer la reconnaissance d\'objets'}
              >
                {modelLoading ? '⏳ Chargement IA...' :
                 detectionEnabled ? '🤖 Arrêter IA' : '🤖 Démarrer IA'}
              </button>
            </div>
          </div>
        </section>

        {/* Section Reconnaissance d'objets */}
        {detectionEnabled && detections.length > 0 && (
          <section className="detection-section">
            <h2>🤖 Objets détectés</h2>
            <div className="detections-grid">
              {detections
                .filter(detection => detection.score > 0.5)
                .map((detection, index) => (
                  <div key={index} className="detection-item">
                    <span className="detection-class">{detection.class}</span>
                    <span className="detection-score">
                      {Math.round(detection.score * 100)}%
                    </span>
                  </div>
                ))}
            </div>
          </section>
        )}

        {/* Section Photos */}
        {photo && (
          <section className="media-section">
            <h2>📸 Photo capturée</h2>
            <div className="media-container">
              <img
                src={URL.createObjectURL(photo)}
                alt="Photo prise"
                className="captured-photo"
              />
              <div className="media-controls">
                <button onClick={handleDownloadPhoto} className="btn btn-download">
                  💾 Télécharger la photo
                </button>
                <button onClick={clearPhoto} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}

        {/* Section Vidéos */}
        {video && (
          <section className="media-section">
            <h2>🎥 Vidéo enregistrée</h2>
            <div className="media-container">
              <video
                controls
                className="captured-video"
                src={URL.createObjectURL(video)}
              />
              <div className="media-controls">
                <button onClick={handleDownloadVideo} className="btn btn-download">
                  💾 Télécharger la vidéo
                </button>
                <button onClick={clearVideo} className="btn btn-clear">
                  🗑️ Supprimer
                </button>
              </div>
            </div>
          </section>
        )}
      </main>
    </div>
  );
}

export default App;