import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [stream, setStream] = useState(null);
  const [photo, setPhoto] = useState(null);
  const [video, setVideo] = useState(null);
  const [recording, setRecording] = useState(false);

  useEffect(() => {
    navigator.mediaDevices.getUserMedia({ video: true, audio: true })
      .then(stream => {
        setStream(stream);
      })
      .catch(error => {
        console.error('Erreur de configuration de la caméra :', error);
      });
  }, []);

  const handleTakePhoto = () => {
    if (stream) {
      const mediaStreamTrack = stream.getVideoTracks()[0];
      const imageCapture = new ImageCapture(mediaStreamTrack);
      imageCapture.takePhoto()
        .then(blob => {
          setPhoto(blob);
        })
        .catch(error => {
          console.error('Erreur de prise de photo :', error);
        });
    }
  };

  const handleStartRecording = () => {
    if (stream) {
      const mediaRecorder = new MediaRecorder(stream);
      const recordedBlobs = [];

      mediaRecorder.ondataavailable = event => {
        recordedBlobs.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const videoBlob = new Blob(recordedBlobs, { type: 'video/webm' });
        setVideo(videoBlob);
      };

      mediaRecorder.start();
      setRecording(true);
    }
  };

  const handleStopRecording = () => {
    if (stream) {
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorder.stop();
      setRecording(false);
    }
  };

  const handleDownloadPhoto = () => {
    if (photo) {
      const url = URL.createObjectURL(photo);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'photo.jpg';
      a.click();
    }
  };

  const handleDownloadVideo = () => {
    if (video) {
      const url = URL.createObjectURL(video);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'video.webm';
      a.click();
    }
  };

  return (
    <div className="App">
      <h1>Caméra</h1>
      {stream && (
        <video
          width="640"
          height="480"
          autoPlay
          playsInline
          srcObject={stream}
        />
      )}
      <button onClick={handleTakePhoto}>Prendre une photo</button>
      <button onClick={handleStartRecording}>Enregistrer une vidéo</button>
      <button onClick={handleStopRecording}>Arrêter l'enregistrement</button>
      {photo && (
        <img
          src={URL.createObjectURL(photo)}
          alt="Photo prise"
        />
      )}
      {video && (
        <video
          width="640"
          height="480"
          controls
          src={URL.createObjectURL(video)}
        />
      )}
      <button onClick={handleDownloadPhoto}>Télécharger la photo</button>
      <button onClick={handleDownloadVideo}>Télécharger la vidéo</button>
    </div>
  );
}

export default App;